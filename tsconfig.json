{"compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "dist", "rootDir": "src", "strict": true, "esModuleInterop": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "moduleResolution": "node", "skipLibCheck": true, "baseUrl": "./src", "paths": {"@/*": ["*"]}}, "include": ["src"], "exclude": ["node_modules", "src/controllers/candidate.controller.ts", "src/controllers/project.controller.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}