-- CreateEnum
CREATE TYPE "public"."upload_purpose" AS ENUM ('cv', 'project_description', 'other');

-- CreateEnum
CREATE TYPE "public"."employment_type" AS ENUM ('full_time', 'part_time', 'contract', 'internship');

-- CreateEnum
CREATE TYPE "public"."experience_level" AS ENUM ('entry', 'junior', 'mid', 'senior', 'executive');

-- CreateEnum
CREATE TYPE "public"."phase_type" AS ENUM ('application', 'screening', 'interview', 'assessment', 'final_review');

-- CreateEnum
CREATE TYPE "public"."phase_status" AS ENUM ('pending', 'active', 'completed', 'cancelled');

-- CreateEnum
CREATE TYPE "public"."candidate_status" AS ENUM ('applied', 'in_review', 'interviewing', 'assessment', 'hired', 'rejected', 'withdrawn');

-- CreateEnum
CREATE TYPE "public"."interview_status" AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled', 'no_show');

-- CreateTable
CREATE TABLE "public"."projects" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "start_date" DATE,
    "end_date" DATE,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "projects_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."file_uploads" (
    "id" SERIAL NOT NULL,
    "file_name" VARCHAR(255) NOT NULL,
    "file_path" VARCHAR(500) NOT NULL,
    "file_size" BIGINT,
    "upload_purpose" "public"."upload_purpose" NOT NULL,
    "project_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "file_uploads_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."job_offers" (
    "id" SERIAL NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "location" VARCHAR(255),
    "salary" VARCHAR(100),
    "employment_type" "public"."employment_type" NOT NULL DEFAULT 'full_time',
    "department" VARCHAR(100),
    "experience_level" "public"."experience_level" NOT NULL DEFAULT 'mid',
    "required_skills" TEXT,
    "application_deadline" DATE,
    "benefits" TEXT,
    "project_id" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "job_offers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."phases" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "type" "public"."phase_type" NOT NULL,
    "start_date" DATE,
    "end_date" DATE,
    "phase_order" INTEGER NOT NULL,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "status" "public"."phase_status" NOT NULL DEFAULT 'pending',
    "passing_score" DECIMAL(5,2),
    "project_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "phases_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."candidates" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "phone" VARCHAR(50),
    "city" VARCHAR(100),
    "parsed_profile" JSONB,
    "cv_file_url" VARCHAR(500),
    "current_phase_id" INTEGER,
    "current_status" "public"."candidate_status" NOT NULL DEFAULT 'applied',
    "application_date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "candidates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."phase_scores" (
    "id" SERIAL NOT NULL,
    "candidate_id" INTEGER NOT NULL,
    "phase_id" INTEGER NOT NULL,
    "score" DECIMAL(5,2),
    "feedback" JSONB,
    "comment" TEXT,
    "evaluated_by" VARCHAR(255),
    "evaluated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "phase_scores_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."interview_templates" (
    "id" SERIAL NOT NULL,
    "phase_id" INTEGER NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "template" JSONB NOT NULL,
    "duration_minutes" INTEGER NOT NULL DEFAULT 60,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "interview_templates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."interviews" (
    "id" SERIAL NOT NULL,
    "candidate_id" INTEGER NOT NULL,
    "interview_template_id" INTEGER NOT NULL,
    "link" VARCHAR(500),
    "scheduled_date" TIMESTAMP(3),
    "actual_start_time" TIMESTAMP(3),
    "actual_end_time" TIMESTAMP(3),
    "responses" TEXT,
    "attachments" VARCHAR(500),
    "status" "public"."interview_status" NOT NULL DEFAULT 'scheduled',
    "interviewer_notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "interviews_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "file_uploads_upload_purpose_idx" ON "public"."file_uploads"("upload_purpose");

-- CreateIndex
CREATE UNIQUE INDEX "job_offers_project_id_key" ON "public"."job_offers"("project_id");

-- CreateIndex
CREATE INDEX "job_offers_is_active_idx" ON "public"."job_offers"("is_active");

-- CreateIndex
CREATE INDEX "job_offers_application_deadline_idx" ON "public"."job_offers"("application_deadline");

-- CreateIndex
CREATE INDEX "phases_project_id_phase_order_idx" ON "public"."phases"("project_id", "phase_order");

-- CreateIndex
CREATE INDEX "phases_status_idx" ON "public"."phases"("status");

-- CreateIndex
CREATE UNIQUE INDEX "phases_project_id_phase_order_key" ON "public"."phases"("project_id", "phase_order");

-- CreateIndex
CREATE UNIQUE INDEX "candidates_email_key" ON "public"."candidates"("email");

-- CreateIndex
CREATE INDEX "candidates_email_idx" ON "public"."candidates"("email");

-- CreateIndex
CREATE INDEX "candidates_current_status_idx" ON "public"."candidates"("current_status");

-- CreateIndex
CREATE INDEX "candidates_current_phase_id_idx" ON "public"."candidates"("current_phase_id");

-- CreateIndex
CREATE INDEX "candidates_application_date_idx" ON "public"."candidates"("application_date");

-- CreateIndex
CREATE INDEX "phase_scores_phase_id_idx" ON "public"."phase_scores"("phase_id");

-- CreateIndex
CREATE INDEX "phase_scores_candidate_id_idx" ON "public"."phase_scores"("candidate_id");

-- CreateIndex
CREATE UNIQUE INDEX "phase_scores_candidate_id_phase_id_key" ON "public"."phase_scores"("candidate_id", "phase_id");

-- CreateIndex
CREATE UNIQUE INDEX "interview_templates_phase_id_key" ON "public"."interview_templates"("phase_id");

-- CreateIndex
CREATE INDEX "interviews_candidate_id_idx" ON "public"."interviews"("candidate_id");

-- CreateIndex
CREATE INDEX "interviews_scheduled_date_idx" ON "public"."interviews"("scheduled_date");

-- CreateIndex
CREATE INDEX "interviews_status_idx" ON "public"."interviews"("status");

-- AddForeignKey
ALTER TABLE "public"."file_uploads" ADD CONSTRAINT "file_uploads_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."job_offers" ADD CONSTRAINT "job_offers_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."phases" ADD CONSTRAINT "phases_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."candidates" ADD CONSTRAINT "candidates_current_phase_id_fkey" FOREIGN KEY ("current_phase_id") REFERENCES "public"."phases"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."phase_scores" ADD CONSTRAINT "phase_scores_candidate_id_fkey" FOREIGN KEY ("candidate_id") REFERENCES "public"."candidates"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."phase_scores" ADD CONSTRAINT "phase_scores_phase_id_fkey" FOREIGN KEY ("phase_id") REFERENCES "public"."phases"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."interview_templates" ADD CONSTRAINT "interview_templates_phase_id_fkey" FOREIGN KEY ("phase_id") REFERENCES "public"."phases"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."interviews" ADD CONSTRAINT "interviews_candidate_id_fkey" FOREIGN KEY ("candidate_id") REFERENCES "public"."candidates"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."interviews" ADD CONSTRAINT "interviews_interview_template_id_fkey" FOREIGN KEY ("interview_template_id") REFERENCES "public"."interview_templates"("id") ON DELETE CASCADE ON UPDATE CASCADE;
