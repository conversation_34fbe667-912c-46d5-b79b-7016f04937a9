{"name": "hiring-system-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "npm run tsoa:gen && ts-node-dev --respawn --transpile-only --require tsconfig-paths/register src/server.ts", "dev:inversify": "ts-node-dev --respawn --transpile-only --require tsconfig-paths/register src/server-inversify.ts", "build": "npm run tsoa:gen && tsc", "build:inversify": "tsc", "start": "node -r module-alias/register dist/server.js", "start:inversify": "node -r module-alias/register dist/server-inversify.js", "seed": "ts-node -r tsconfig-paths/register src/scripts/seed.ts", "test:scoring": "ts-node -r tsconfig-paths/register src/scripts/test-scoring.ts", "tsoa:gen": "tsoa routes && tsoa spec", "tsoa:gen:dev": "tsoa routes && tsoa spec --outputDirectory src/generated", "tsoa:gen:prod": "tsoa routes && tsoa spec --outputDirectory dist", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev"}, "repository": {"type": "git", "url": "git+https://github.com/passisto-enterprise/hiring-system-backend.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/passisto-enterprise/hiring-system-backend/issues"}, "homepage": "https://github.com/passisto-enterprise/hiring-system-backend#readme", "dependencies": {"@prisma/client": "^6.14.0", "dotenv": "^16.0.0", "express": "^4.21.2", "hrflow": "^0.1.2", "inversify": "^6.2.2", "inversify-binding-decorators": "^4.0.0", "inversify-express-utils": "^6.5.0", "module-alias": "^2.2.3", "multer": "^1.4.5-lts.1", "openai": "^4.0.0", "reflect-metadata": "^0.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsoa": "^6.4.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/multer": "^1.4.12", "@types/node": "^24.2.1", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.0", "prisma": "^6.14.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.9.2"}, "_moduleAliases": {"@": "dist"}}