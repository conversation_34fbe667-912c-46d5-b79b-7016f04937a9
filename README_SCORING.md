# Hiring System Backend - Scoring System Implementation

This document describes the implementation of the AI-powered candidate scoring system migrated from the Python application to the Express.js backend.

## Features Implemented

### 🎯 Core Scoring Features
- **AI-Powered CV Scoring**: Uses OpenAI GPT-4 for intelligent candidate evaluation
- **HRFlow Integration**: Automatic CV parsing and data extraction
- **Multi-Dimensional Scoring**: Evaluates candidates across 6 key dimensions:
  - Education Relevance (25%)
  - Skills Match (25%)
  - Experience Quality (20%)
  - Technical Proficiency (15%)
  - Career Progression (10%)
  - Language Fit (5%)

### 📊 Scoring Capabilities
- **Individual Candidate Scoring**: Score candidates manually or automatically
- **Bulk Scoring**: Score all candidates in a project at once
- **Fallback Scoring**: Rule-based scoring when AI is unavailable
- **Configurable Weights**: Customize scoring dimensions per project
- **Security**: Prompt injection protection and input sanitization

### 🔄 Workflow Integration
- **Phase-Based Scoring**: Candidates are scored in their current phase
- **Automatic Progression**: Only selected candidates advance to next phase
- **Score History**: Track scoring changes over time via PhaseScore table

## API Endpoints

### Project Endpoints
```
GET    /projects/:id/candidates           # Get all candidates for a project
POST   /projects/:id/candidates/upload    # Upload CVs with auto-scoring
POST   /projects/:id/score-all           # Score all candidates in project
```

### Candidate Endpoints
```
GET    /candidates                       # Get all candidates
GET    /candidates/:id                   # Get candidate by ID
POST   /candidates                       # Create new candidate
PUT    /candidates/:id                   # Update candidate
DELETE /candidates/:id                   # Delete candidate
POST   /candidates/:candidateId/score/:phaseId  # Score specific candidate
```

## Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Configuration
Copy `.env.example` to `.env` and configure:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/hiring_system_db"

# HRFlow API Configuration
HRFLOW_API_KEY="your_hrflow_api_key_here"
HRFLOW_USER_EMAIL="your_hrflow_user_email_here"
HRFLOW_SOURCE_KEY="your_hrflow_source_key_here"

# OpenAI Configuration
OPENAI_API_KEY="your_openai_api_key_here"

# Application Configuration
NODE_ENV="development"
PORT=3000
MAX_FILE_SIZE_MB=10
UPLOAD_DIR="./uploads"
```

### 3. Database Setup
```bash
# Generate Prisma client
npm run prisma:generate

# Run database migrations
npm run prisma:migrate
```

### 4. Start the Server
```bash
# Development
npm run dev

# Production
npm run build
npm start
```

## Usage Examples

### Upload and Score CVs
```bash
curl -X POST \
  http://localhost:3000/projects/1/candidates/upload \
  -F "files=@candidate1.pdf" \
  -F "files=@candidate2.pdf" \
  -F "autoScore=true"
```

### Score All Candidates
```bash
curl -X POST http://localhost:3000/projects/1/score-all
```

### Get Scored Candidates
```bash
curl http://localhost:3000/projects/1/candidates
```

## Database Schema Changes

The implementation uses your existing database schema with minimal changes:

### Existing Tables Used
- **candidates**: Stores candidate data with `parsedProfile` JSON field
- **phase_scores**: Stores scoring results with `feedback` JSON field
- **job_offers**: Stores job requirements in `requiredSkills` field
- **phases**: Manages recruitment phases

### JSON Data Structures

**Candidate.parsedProfile**:
```json
{
  "personal_info": {"name": "John Doe", "email": "<EMAIL>"},
  "education": [{"degree": "BS Computer Science", "university": "MIT"}],
  "experience": [{"title": "Senior Developer", "company": "TechCorp"}],
  "skills": ["JavaScript", "React", "Node.js"],
  "languages": ["English", "Spanish"]
}
```

**PhaseScore.feedback**:
```json
{
  "scores": {
    "education_relevance": {"score": 85, "reasoning": "Strong background"},
    "skills_match": {"score": 92, "reasoning": "Excellent match"}
  },
  "final_score": 84.5,
  "recommendation": "Highly Recommended",
  "confidence_level": "High",
  "flags": ["strong_technical_background"]
}
```

## Architecture

### Services
- **ScoringService**: Handles AI-powered scoring logic
- **HRFlowService**: Manages CV parsing integration
- **CandidateService**: Orchestrates candidate operations
- **ProjectService**: Manages project-related operations

### Controllers
- **CandidateController**: Candidate CRUD and scoring endpoints
- **ProjectController**: Project management and bulk operations

### Dependency Injection
Uses Inversify for IoC container management with proper service registration.

## Security Features

- **Input Sanitization**: Removes potentially harmful content from CVs
- **Prompt Injection Protection**: Prevents AI manipulation attempts
- **File Type Validation**: Only allows PDF, DOC, DOCX files
- **File Size Limits**: Configurable upload size restrictions

## Error Handling

- **Graceful Degradation**: Falls back to rule-based scoring if AI fails
- **Comprehensive Logging**: Detailed error tracking and debugging
- **Validation**: Input validation at all levels
- **Transaction Safety**: Proper database transaction handling

## Testing

The system includes comprehensive error handling and fallback mechanisms. Test with:

1. Valid CV uploads
2. Invalid file types
3. Large files
4. Network failures
5. Invalid API keys

## Monitoring

Monitor the following:
- API response times
- Scoring accuracy
- File upload success rates
- AI service availability
- Database performance
