import { Request, Response, NextFunction } from 'express';

/**
 * Request logging middleware
 */
export function requestLogger(req: Request, res: Response, next: NextFunction): void {
  const startTime = Date.now();
  const requestId = req.headers['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Add request ID to headers for tracking
  req.headers['x-request-id'] = requestId as string;
  res.setHeader('X-Request-ID', requestId);

  // Standard HTTP log format: METHOD /path - IP - UserAgent
  const userAgent = req.get('User-Agent') || '-';
  console.log(`${req.method} ${req.originalUrl} - ${req.ip} - "${userAgent}" [${requestId}]`);

  // Log query params if present
  if (Object.keys(req.query).length > 0) {
    console.log(`  Query: ${JSON.stringify(req.query)}`);
  }

  // Log route params if present
  if (Object.keys(req.params).length > 0) {
    console.log(`  Params: ${JSON.stringify(req.params)}`);
  }

  // Log body if present (truncate if too large)
  if (req.body && Object.keys(req.body).length > 0) {
    const bodyStr = JSON.stringify(req.body);
    if (bodyStr.length > 200) {
      console.log(`  Body: ${bodyStr.substring(0, 200)}...`);
    } else {
      console.log(`  Body: ${bodyStr}`);
    }
  }

  // Override res.json to log responses
  const originalJson = res.json;
  res.json = function(body: any) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Standard response log: METHOD /path - STATUS - Duration - [RequestID]
    console.log(`${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms [${requestId}]`);

    // Log response body if it's an error or in development
    if (res.statusCode >= 400 || process.env.NODE_ENV === 'development') {
      const bodyStr = JSON.stringify(body);
      if (bodyStr.length > 500) {
        console.log(`  Response: ${bodyStr.substring(0, 500)}...`);
      } else {
        console.log(`  Response: ${bodyStr}`);
      }
    }

    return originalJson.call(this, body);
  };

  // Override res.send to log non-JSON responses
  const originalSend = res.send;
  res.send = function(body: any) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Only log if json wasn't already called
    if (!res.headersSent || res.getHeader('content-type')?.toString().includes('text/html')) {
      console.log(`${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms [${requestId}]`);

      // Log non-JSON response body if it's an error or small
      if (res.statusCode >= 400 && typeof body === 'string' && body.length < 500) {
        console.log(`  Response: ${body}`);
      }
    }

    return originalSend.call(this, body);
  };

  next();
}

/**
 * Performance monitoring middleware
 */
export function performanceMonitor(req: Request, res: Response, next: NextFunction): void {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();

  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    const memoryDelta = ((endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024);

    // Only log performance for slow requests or high memory usage
    if (duration > 1000 || Math.abs(memoryDelta) > 10) {
      console.log(`PERF ${req.method} ${req.originalUrl} - ${duration.toFixed(2)}ms - ${memoryDelta.toFixed(2)}MB`);
    }
  });

  next();
}
