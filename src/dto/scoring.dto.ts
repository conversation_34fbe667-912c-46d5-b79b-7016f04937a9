export interface JobRequirements {
  required_education?: string;
  preferred_fields?: string[];
  min_experience_years?: number;
  preferred_experience_years?: number;
  required_skills?: string[];
  preferred_skills?: string[];
  technical_requirements?: Record<string, string[]>;
  required_languages?: string[];
  location_requirements?: string;
  remote_work?: boolean;
}

export interface ScoringConfig {
  weights?: {
    education_relevance?: number;
    skills_match?: number;
    experience_quality?: number;
    technical_proficiency?: number;
    career_progression?: number;
    language_fit?: number;
  };
  passing_thresholds?: {
    minimum_score?: number;
    recommended_score?: number;
  };
}

export interface ScoringDimension {
  score: number;
  reasoning: string;
}

export interface ScoringResponse {
  scores: {
    education_relevance: ScoringDimension;
    skills_match: ScoringDimension;
    experience_quality: ScoringDimension;
    technical_proficiency: ScoringDimension;
    career_progression: ScoringDimension;
    language_fit: ScoringDimension;
  };
  final_score: number;
  recommendation: string;
  confidence_level: string;
  flags: string[];
  scored_at: string;
}

export interface PhaseScoreCreateDTO {
  candidateId: number;
  phaseId: number;
  score?: number;
  feedback?: any;
  comment?: string;
  evaluatedBy?: string;
}

export interface PhaseScoreUpdateDTO {
  score?: number;
  feedback?: any;
  comment?: string;
  evaluatedBy?: string;
}

export interface PhaseScoreResponseDTO {
  id: number;
  candidateId: number;
  phaseId: number;
  score?: number;
  feedback?: any;
  comment?: string;
  evaluatedBy?: string;
  evaluatedAt: Date;
}
