/**
 * Personal information from parsed CV
 */
export interface PersonalInfo {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  summary?: string;
}

/**
 * Education information from parsed CV
 */
export interface Education {
  degree?: string;
  institution?: string;
  graduationYear?: number;
  field?: string;
  gpa?: string;
}

/**
 * Work experience information from parsed CV
 */
export interface Experience {
  title?: string;
  company?: string;
  startDate?: string;
  endDate?: string;
  description?: string;
  location?: string;
  duration?: string;
}

/**
 * Complete parsed profile from CV
 */
export interface ParsedProfile {
  personal_info?: PersonalInfo;
  education?: Education[];
  experience?: Experience[];
  skills?: string[];
  languages?: string[];
  certifications?: string[];
}

/**
 * Individual score detail with reasoning
 */
export interface ScoreDetail {
  /** Score value between 0-100 */
  score: number;
  /** Explanation for the score */
  reasoning: string;
}

/**
 * Complete scoring results for a candidate
 */
export interface ScoringResults {
  scores: {
    education_relevance?: ScoreDetail;
    skills_match?: ScoreDetail;
    experience_quality?: ScoreDetail;
    technical_proficiency?: ScoreDetail;
    career_progression?: ScoreDetail;
    language_fit?: ScoreDetail;
  };
  /** Final aggregated score */
  final_score: number;
  /** AI recommendation */
  recommendation: string;
  /** Confidence level of the scoring */
  confidence_level: string;
  /** Any flags or warnings */
  flags: string[];
  /** Timestamp when scoring was completed */
  scored_at: string;
}

/**
 * Data required to create a new candidate
 */
export interface CreateCandidateDTO {
  /** Candidate's full name */
  name: string;
  /** Candidate's email address */
  email: string;
  /** Candidate's phone number */
  phone?: string;
  /** Candidate's city */
  city?: string;
  /** Current phase ID in the hiring process */
  currentPhaseId?: number;
}

/**
 * Data for updating an existing candidate
 */
export interface UpdateCandidateDTO {
  /** Candidate's full name */
  name?: string;
  /** Candidate's email address */
  email?: string;
  /** Candidate's phone number */
  phone?: string;
  /** Candidate's city */
  city?: string;
  /** Current phase ID in the hiring process */
  currentPhaseId?: number;
  /** Current status in the hiring process */
  currentStatus?: string;
}

/**
 * Complete candidate information returned by API
 */
export interface CandidateResponseDTO {
  /** Unique candidate identifier */
  id: number;
  /** Candidate's full name */
  name: string;
  /** Candidate's email address */
  email: string;
  /** Candidate's phone number */
  phone?: string;
  /** Candidate's city */
  city?: string;
  /** Parsed CV profile data */
  parsedProfile?: ParsedProfile;
  /** URL to the uploaded CV file */
  cvFileUrl?: string;
  /** Current phase ID in the hiring process */
  currentPhaseId?: number;
  /** Current status in the hiring process */
  currentStatus: string;
  /** Date when candidate applied */
  applicationDate: string;
  /** Last update timestamp */
  updatedAt: string;
}

/**
 * Options for uploading candidate CVs
 */
export interface UploadCandidateDTO {
  /** Whether to automatically score the candidate after upload */
  autoScore?: boolean;
}

/**
 * Data for scoring a specific candidate
 */
export interface ScoreCandidateDTO {
  /** ID of the candidate to score */
  candidateId: number;
  /** ID of the phase to score for */
  phaseId: number;
}

/**
 * Data for bulk scoring all candidates in a project
 */
export interface BulkScoreDTO {
  /** ID of the project to score candidates for */
  projectId: number;
}
