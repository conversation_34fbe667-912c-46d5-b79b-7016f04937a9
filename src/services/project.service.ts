
import prisma from "@/config/prisma";
import { CreateProjectDTO, UpdateProjectDTO } from "@/dto/project.dto";
import { IProjectService } from "../interfaces/IProjectService";
import { provideSingleton } from "@/utils/provideSingleton";
import { TYPES } from "@/config/types";

interface ServiceError extends Error {
  code?: string;
  details?: any;
  service?: string;
  method?: string;
}

@provideSingleton(TYPES.IProjectService)
export class ProjectService implements IProjectService {
  async create(data: CreateProjectDTO) {
    return prisma.project.create({ data });
  }

  async findAll() {
    try {
      console.log("DB ProjectService.findAll - Starting query");

      const projects = await prisma.project.findMany();
      console.log(`DB ProjectService.findAll - Found ${projects.length} projects`);

      return projects;
    } catch (error) {
      console.error(`DB ERROR ProjectService.findAll - ${error instanceof Error ? error.message : String(error)}`);

      if (process.env.NODE_ENV === 'development' && error instanceof Error) {
        console.error(`  Stack: ${error.stack}`);
      }

      // Create enhanced error with service context
      const serviceError: ServiceError = new Error(`Database query failed: ${error instanceof Error ? error.message : String(error)}`);
      serviceError.code = 'DATABASE_QUERY_ERROR';
      serviceError.service = 'ProjectService';
      serviceError.method = 'findAll';
      serviceError.details = {
        originalError: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error,
        query: 'prisma.project.findMany()',
        timestamp: new Date().toISOString()
      };

      throw serviceError;
    }
  }

  async findById(id: number) {
    return prisma.project.findUnique({ where: { id } });
  }

  async update(id: number, data: UpdateProjectDTO) {
    return prisma.project.update({ where: { id }, data });
  }

  async delete(id: number) {
    return prisma.project.delete({ where: { id } });
  }
}