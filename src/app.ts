import 'reflect-metadata';
// Import process error handlers first
import '@/utils/processErrorHandlers';

import express, { Express } from 'express';
import swaggerUi from 'swagger-ui-express';
import { RegisterRoutes } from '@/generated/routes';
import swaggerDocument from '@/generated/swagger.json';
import { container } from '@/config/inversify.config';
import { TYPES } from '@/config/types';
import { requestLogger, performanceMonitor } from '@/middleware/requestLogger';
import { errorHandler, notFoundHandler } from '@/middleware/errorHandler';

console.log("APP Starting application initialization");

// Create Express app
const app: Express = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', true);

// Request logging and monitoring middleware (before all routes)
if (process.env.NODE_ENV === 'development') {
  app.use(requestLogger);
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Add request ID to all requests
app.use((req, res, next) => {
  if (!req.headers['x-request-id']) {
    req.headers['x-request-id'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  next();
});

// Test container initialization
try {
  const projectService = container.get(TYPES.IProjectService);
  const candidateService = container.get(TYPES.ICandidateService);
  console.log("APP Container services initialized successfully");
} catch (error) {
  console.error(`APP ERROR Container initialization failed - ${error instanceof Error ? error.message : String(error)}`);
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version,
    environment: process.env.NODE_ENV || 'development'
  });
});

// Register TSOA routes
try {
  RegisterRoutes(app);
  console.log("APP TSOA routes registered successfully");
} catch (error) {
  console.error(`APP ERROR TSOA route registration failed - ${error instanceof Error ? error.message : String(error)}`);
  throw error;
}

// Setup Swagger UI with TSOA-generated spec
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Hiring System API Documentation'
}));
console.log("APP Swagger UI configured at /api-docs");

// 404 handler (must be after all routes)
app.use(notFoundHandler);

// Global error handler (must be last)
app.use(errorHandler);

console.log("APP Error handling middleware configured");

// Export the app instance
export { app };