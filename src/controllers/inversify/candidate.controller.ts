import * as express from "express";
import { interfaces, controller, httpGet, httpPost, httpPut, httpDelete, request, response, requestParam, requestBody } from "inversify-express-utils";
import { inject } from "inversify";
import { TYPES } from "@/config/types";
import { ICandidateService } from "@/interfaces/ICandidateService";
import { CreateCandidateDTO, UpdateCandidateDTO, CandidateResponseDTO } from "@/dto/candidate.dto";


@controller("/candidates")
export class CandidateController implements interfaces.Controller {

    constructor(
        @inject(TYPES.ICandidateService) private candidateService: ICandidateService
    ) {}

    @httpGet("/")
    private async getAllCandidates(@request() req: express.Request, @response() res: express.Response): Promise<void> {
        try {
            const candidates = await this.candidateService.findAll();
            res.json(candidates);
        } catch (error) {
            console.error(`CONTROLLER ERROR CandidateController.getAllCandidates - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to fetch candidates" });
        }
    }

    @httpGet("/:id")
    private async getCandidate(@requestParam("id") id: string, @response() res: express.Response): Promise<void> {
        try {
            const candidateId = parseInt(id);
            const candidate = await this.candidateService.findById(candidateId);
            
            if (!candidate) {
                res.status(404).json({ error: "Candidate not found" });
                return;
            }
            
            res.json(candidate);
        } catch (error) {
            console.error(`CONTROLLER ERROR CandidateController.getCandidate - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to fetch candidate" });
        }
    }

    @httpPost("/")
    private async createCandidate(@requestBody() candidateData: CreateCandidateDTO, @response() res: express.Response): Promise<void> {
        try {
            const candidate = await this.candidateService.create(candidateData);
            res.status(201).json(candidate);
        } catch (error) {
            console.error(`CONTROLLER ERROR CandidateController.createCandidate - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to create candidate" });
        }
    }

    @httpPut("/:id")
    private async updateCandidate(@requestParam("id") id: string, @requestBody() candidateData: UpdateCandidateDTO, @response() res: express.Response): Promise<void> {
        try {
            const candidateId = parseInt(id);
            const candidate = await this.candidateService.update(candidateId, candidateData);
            
            if (!candidate) {
                res.status(404).json({ error: "Candidate not found" });
                return;
            }
            
            res.json(candidate);
        } catch (error) {
            console.error(`CONTROLLER ERROR CandidateController.updateCandidate - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to update candidate" });
        }
    }

    @httpDelete("/:id")
    private async deleteCandidate(@requestParam("id") id: string, @response() res: express.Response): Promise<void> {
        try {
            const candidateId = parseInt(id);
            const success = await this.candidateService.delete(candidateId);
            
            if (!success) {
                res.status(404).json({ error: "Candidate not found" });
                return;
            }
            
            res.status(204).send();
        } catch (error) {
            console.error(`CONTROLLER ERROR CandidateController.deleteCandidate - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to delete candidate" });
        }
    }

    @httpPost("/:candidateId/score/:phaseId")
    private async scoreCandidate(@requestParam("candidateId") candidateId: string, @requestParam("phaseId") phaseId: string, @response() res: express.Response): Promise<void> {
        try {
            const candidateIdNum = parseInt(candidateId);
            const phaseIdNum = parseInt(phaseId);
            
            await this.candidateService.scoreCandidate(candidateIdNum, phaseIdNum);
            
            res.json({
                success: true,
                message: "Candidate scored successfully"
            });
        } catch (error) {
            console.error(`CONTROLLER ERROR CandidateController.scoreCandidate - ${error instanceof Error ? error.message : String(error)}`);
            res.status(500).json({ error: "Failed to score candidate" });
        }
    }
}
