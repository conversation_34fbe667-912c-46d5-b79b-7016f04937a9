import { Controller, Get, Post, Put, Delete, Route, Tags, Body, Path, SuccessResponse } from "tsoa";
import { container } from "@/config/inversify.config";
import { IProjectService } from "@/interfaces/IProjectService";
import { ICandidateService } from "@/interfaces/ICandidateService";
import { CreateProjectDTO, UpdateProjectDTO, ProjectResponseDTO } from "@/dto/project.dto";
import { CandidateResponseDTO } from "@/dto/candidate.dto";
import { TYPES } from "@/config/types";
import { CustomError } from "@/middleware/errorHandler";

@Route("projects")
@Tags("Projects")
export class ProjectTsoaController extends Controller {
  private projectService: IProjectService;
  private candidateService: ICandidateService;

  constructor() {
    super();

    try {
      this.projectService = container.get<IProjectService>(TYPES.IProjectService);
      this.candidateService = container.get<ICandidateService>(TYPES.ICandidateService);
    } catch (error) {
      console.error(`DI ERROR ProjectTsoaController - ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Get all projects
   */
  @Get("/")
  @SuccessResponse("200", "List of projects")
  public async getProjects(): Promise<ProjectResponseDTO[]> {
    try {
      const projects = await this.projectService.findAll();

      const mappedProjects = projects.map((project: any) => ({
        ...project,
        startDate: project.startDate?.toISOString() || null,
        endDate: project.endDate?.toISOString() || null,
        createdAt: project.createdAt.toISOString(),
        updatedAt: project.updatedAt.toISOString()
      }));

      return mappedProjects;
    } catch (error) {
      console.error(`CONTROLLER ERROR ProjectTsoaController.getProjects - ${error instanceof Error ? error.message : String(error)}`);

      // Create a custom error with more details
      const customError: CustomError = new Error(`Failed to fetch projects: ${error instanceof Error ? error.message : String(error)}`);
      customError.status = 500;
      customError.code = 'PROJECT_FETCH_ERROR';
      customError.details = {
        originalError: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error,
        controller: 'ProjectTsoaController',
        method: 'getProjects',
        timestamp: new Date().toISOString()
      };

      this.setStatus(500);
      throw customError;
    }
  }

  /**
   * Get project by ID
   */
  @Get("/{id}")
  @SuccessResponse("200", "Project details")
  public async getProject(@Path() id: number): Promise<ProjectResponseDTO> {
    try {
      const project = await this.projectService.findById(id);
      
      if (!project) {
        this.setStatus(404);
        throw new Error("Project not found");
      }
      
      return {
        ...project,
        startDate: project.startDate?.toISOString() || null,
        endDate: project.endDate?.toISOString() || null,
        createdAt: project.createdAt.toISOString(),
        updatedAt: project.updatedAt.toISOString()
      };
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to fetch project");
    }
  }

  /**
   * Create a new project
   */
  @Post("/")
  @SuccessResponse("201", "Project created successfully")
  public async createProject(@Body() projectData: CreateProjectDTO): Promise<ProjectResponseDTO> {
    try {
      const project = await this.projectService.create(projectData);
      this.setStatus(201);
      return {
        ...project,
        startDate: project.startDate?.toISOString() || null,
        endDate: project.endDate?.toISOString() || null,
        createdAt: project.createdAt.toISOString(),
        updatedAt: project.updatedAt.toISOString()
      };
    } catch (error) {
      this.setStatus(500);
      console.error(`CONTROLLER ERROR ProjectTsoaController.createProject - ${error instanceof Error ? error.message : String(error)}`);
      throw new Error("Failed to create project");
    }
  }

  /**
   * Update project
   */
  @Put("/{id}")
  @SuccessResponse("200", "Project updated successfully")
  public async updateProject(@Path() id: number, @Body() projectData: UpdateProjectDTO): Promise<ProjectResponseDTO> {
    try {
      const project = await this.projectService.update(id, projectData);
      return {
        ...project,
        startDate: project.startDate?.toISOString() || null,
        endDate: project.endDate?.toISOString() || null,
        createdAt: project.createdAt.toISOString(),
        updatedAt: project.updatedAt.toISOString()
      };
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to update project");
    }
  }

  /**
   * Delete project
   */
  @Delete("/{id}")
  @SuccessResponse("204", "Project deleted successfully")
  public async deleteProject(@Path() id: number): Promise<void> {
    try {
      await this.projectService.delete(id);
      this.setStatus(204);
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to delete project");
    }
  }

  /**
   * Get all candidates for a project
   */
  @Get("/{id}/candidates")
  @SuccessResponse("200", "List of candidates for the project")
  public async getProjectCandidates(@Path() id: number): Promise<CandidateResponseDTO[]> {
    try {
      const candidates = await this.candidateService.findByProject(id);
      return candidates as CandidateResponseDTO[];
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to fetch project candidates");
    }
  }

  /**
   * Score all candidates in a project
   */
  @Post("/{id}/score-all")
  @SuccessResponse("200", "Bulk scoring completed")
  public async scoreAllCandidates(@Path() id: number): Promise<any> {
    try {
      const result = await this.candidateService.scoreAllCandidates(id);
      return result;
    } catch (error) {
      this.setStatus(500);
      throw new Error("Failed to score candidates");
    }
  }
}
