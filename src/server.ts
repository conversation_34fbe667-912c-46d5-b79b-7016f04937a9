if (process.env.NODE_ENV === "production") {
  require("module-alias/register");
}
import { app } from './app';
import { validateConfig, appConfig } from './config/config';

// Validate configuration before starting the server
validateConfig();

const PORT = appConfig.port;

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📊 Environment: ${appConfig.nodeEnv}`);
  console.log(`📁 Upload directory: ${appConfig.uploadDir}`);
});
