import 'reflect-metadata';
// Import process error handlers first
import '@/utils/processErrorHandlers';

import express, { Express } from 'express';
import { InversifyExpressServer } from 'inversify-express-utils';
import { container } from '@/config/inversify.config';
import { requestLogger } from '@/middleware/requestLogger';
import { errorHand<PERSON>, notFoundHandler } from '@/middleware/errorHandler';

console.log("APP Starting application initialization with inversify-express-utils");

// Create server with inversify-express-utils
const server = new InversifyExpressServer(container);

server.setConfig((app: Express) => {
  // Trust proxy for accurate IP addresses
  app.set('trust proxy', true);

  // Request logging and monitoring middleware (before all routes)
  if (process.env.NODE_ENV === 'development') {
    app.use(requestLogger);
  }

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Add request ID to all requests
  app.use((req, res, next) => {
    if (!req.headers['x-request-id']) {
      req.headers['x-request-id'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    next();
  });

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
      environment: process.env.NODE_ENV || 'development'
    });
  });
});

server.setErrorConfig((app: Express) => {
  // Error handling middleware (after all routes)
  app.use(notFoundHandler);
  app.use(errorHandler);
});

// Build the app
const app = server.build();

console.log("APP Inversify Express server built successfully");

export { app };
