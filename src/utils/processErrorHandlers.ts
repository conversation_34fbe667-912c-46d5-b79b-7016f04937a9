/**
 * Global process error handlers for catching all unhandled errors
 */

/**
 * Handle uncaught exceptions
 */
process.on('uncaughtException', (error: Error) => {
  console.error('💥 UNCAUGHT EXCEPTION:');
  console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.error('🚨 This is a critical error that could crash the application!');
  console.error(`📅 Timestamp: ${new Date().toISOString()}`);
  console.error(`❌ Error Name: ${error.name}`);
  console.error(`💬 Error Message: ${error.message}`);
  console.error('📚 Stack Trace:');
  console.error(error.stack);
  console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  // In development, don't exit the process
  if (process.env.NODE_ENV === 'development') {
    console.error('🔧 Development mode: Continuing execution...');
  } else {
    console.error('🛑 Production mode: Exiting process...');
    process.exit(1);
  }
});

/**
 * Handle unhandled promise rejections
 */
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  console.error('🔥 UNHANDLED PROMISE REJECTION:');
  console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.error('🚨 A promise was rejected but no error handler was attached!');
  console.error(`📅 Timestamp: ${new Date().toISOString()}`);
  console.error('🎯 Promise:', promise);
  console.error('❌ Rejection Reason:');
  
  if (reason instanceof Error) {
    console.error(`   Name: ${reason.name}`);
    console.error(`   Message: ${reason.message}`);
    console.error(`   Stack: ${reason.stack}`);
  } else {
    console.error(`   Reason: ${JSON.stringify(reason, null, 2)}`);
  }
  
  console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  // In development, don't exit the process
  if (process.env.NODE_ENV === 'development') {
    console.error('🔧 Development mode: Continuing execution...');
  } else {
    console.error('🛑 Production mode: Exiting process...');
    process.exit(1);
  }
});

/**
 * Handle process warnings
 */
process.on('warning', (warning: Error) => {
  console.warn('⚠️  PROCESS WARNING:');
  console.warn('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.warn(`📅 Timestamp: ${new Date().toISOString()}`);
  console.warn(`⚠️  Warning Name: ${warning.name}`);
  console.warn(`💬 Warning Message: ${warning.message}`);
  if (warning.stack) {
    console.warn('📚 Stack Trace:');
    console.warn(warning.stack);
  }
  console.warn('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
});

/**
 * Handle SIGTERM gracefully
 */
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received. Shutting down gracefully...');
  console.log(`📅 Timestamp: ${new Date().toISOString()}`);
  process.exit(0);
});

/**
 * Handle SIGINT gracefully (Ctrl+C)
 */
process.on('SIGINT', () => {
  console.log('🛑 SIGINT received. Shutting down gracefully...');
  console.log(`📅 Timestamp: ${new Date().toISOString()}`);
  process.exit(0);
});

export {}; // Make this a module
