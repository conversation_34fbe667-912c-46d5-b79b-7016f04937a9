{"entryFile": "src/server.ts", "controllerPathGlobs": ["src/controllers/tsoa/**/*.tsoa.controller.ts"], "noImplicitAdditionalProperties": "throw-on-extras", "spec": {"outputDirectory": "src/generated", "specVersion": 3, "specFileBaseName": "swagger", "name": "Hiring System API", "description": "API documentation for the Hiring System backend", "version": "1.0.0", "host": "localhost:3000", "basePath": "/", "schemes": ["http"], "consumes": ["application/json"], "produces": ["application/json"]}, "routes": {"routesDir": "src/generated", "middleware": "express"}, "compilerOptions": {"baseUrl": "./src", "paths": {"@/*": ["*"]}}}